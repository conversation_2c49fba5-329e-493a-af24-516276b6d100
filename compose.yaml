# Comments are provided throughout this file to help you get started.
# If you need more help, visit the Docker compose reference guide at
# https://docs.docker.com/compose/compose-file/

# Here the instructions define your application as a service called "server".
# This service is built from the Dockerfile in the current directory.
# You can add other services your application may depend on here, such as a
# database or a cache. For examples, see the Awesome Compose repository:
# https://github.com/docker/awesome-compose
services:
  app:
    build:
      context: .
    container_name: filestack-shopify
    environment:
      - SHOPIFY_API_KEY=<SHOPIFY_API_KEY>
      - SHOPIFY_SECRET=<SHOPIFY_SECRET>
      - APP_NAME=<APP_NAME>
      - SERVER_HOSTNAME=0.0.0.0:8000
      - SERVER_BASE_URL=http://0.0.0.0:8000
      - INSTALL_REDIRECT_URL=http://0.0.0.0:8000/app_installed
      - WEBHOOK_APP_UNINSTALL_URL=http://0.0.0.0:8000/app_uninstalled
      - DATABASE=/data/filestack.db
      - PORT=8000
      - FILESTACK_USER_NAME=filestack_user_name
      - FILESTACK_PASSWORD=filestack_password
      - FILESTACK_ENDPOINT=filestack_end_point
    ports:
      - 8000:8000
    volumes:
      - db-data:/data
    depends_on:
      - db

  db:
    container_name: sqlite
    image: keinos/sqlite3:latest
    volumes:
      - db-data:/data
    ports:
      - "5432:5432"
    command: "tail -f /dev/null"

volumes:
  db-data:
    name: "sqlite-data"

# The commented out section below is an example of how to define a PostgreSQL
# database that your application can use. `depends_on` tells Docker Compose to
# start the database before your application. The `db-data` volume persists the
# database data between container restarts. The `db-password` secret is used
# to set the database password. You must create `db/password.txt` and add
# a password of your choosing to it before running `docker compose up`.
#     depends_on:
#       db:
#         condition: service_healthy
#   db:
#     image: postgres
#     restart: always
#     user: postgres
#     secrets:
#       - db-password
#     volumes:
#       - db-data:/var/lib/postgresql/data
#     environment:
#       - POSTGRES_DB=example
#       - POSTGRES_PASSWORD_FILE=/run/secrets/db-password
#     expose:
#       - 5432
#     healthcheck:
#       test: [ "CMD", "pg_isready" ]
#       interval: 10s
#       timeout: 5s
#       retries: 5
# volumes:
#   db-data:
# secrets:
#   db-password:
#     file: db/password.txt

