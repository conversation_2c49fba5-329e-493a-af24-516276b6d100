<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel= "stylesheet" type= "text/css" href= "{{ url_for('static',filename='styles/index.css') }}">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script lang="javascript" src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
    <title>Document</title>
</head>

<body>
    <div>
        <div class="header">
            <h3>Settings</h3>
            <button>Customer Support</button>
        </div>
        <div class="main-container">
            
            <div class="setting-container">

                <div class="settingContent-container">
                    <form name="frmFilestackLogin" id="frmFilestackLogin" method="POST">
                    <div class="tabContent defaultOpen ">
                        <div class="account-setting">
                            <div class="card">
                                <div class="label-input">
                                    <label for="filestack_email">Filestack Email</label>
                                    <input type="text" id="filestack_email" placeholder="Filestack Email" name="filestack_email" />
                                </div>
                            </div>
                            <div class="card">
                                <div class="label-input">
                                    <label for="filestack_password">Filestack Password</label>
                                    <input type="password" id="filestack_password" name="filestack_password" />
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <button class="black-btn" name="btnLogin" id="btn-login">Login</button>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        const tabBtn = document.querySelectorAll(".tabBtn");
        const tabContent = document.querySelectorAll(".tabContent")
        tabContent.forEach(content => content.style.display = "none");
        document.querySelector(".defaultOpen").style.display = "block";
        tabBtn.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                tabBtn.forEach(btn => btn.classList.remove('activeTab'));
                tabContent.forEach(content => content.style.display = "none");
                tab.classList.add('activeTab');
                tabContent[index].style.display = "block"
            })
        })
    
        $(document).ready(function(){
            $('#btn-login').click(function(){
                $.ajax({
                    type: "POST",
                    url: '/auth/filestack',
                    data: $('#frmFilestackLogin').serialize(), // serializes the form's elements.
                    success: function(data)
                    {
                        if(data.status){
                            window.location.reload()
                        }
                        else{
                            alert(data.msg)
                        }
                    }
            });
                return false;
            })
        })
    </script>
</body>

</html>