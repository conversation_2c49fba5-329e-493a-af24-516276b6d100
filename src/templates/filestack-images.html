{% extends 'base.html' %}

{% block content %}
<style>

.paginate-button {
    padding: 0 7px 0 7px;
    border: 0;
    font-size: 15px;
    cursor: pointer;
}
</style>
<div>

  <div id="product-images">
  
    <div class="Polaris-LegacyCard" style="margin: 0 20px;">
      <div class="Polaris-IndexFilters__IndexFiltersWrapper">
        <div class="Polaris-IndexFilters">
          <div>
            <div class="Polaris-IndexFilters-Container" style="display: flex; justify-content: right;">
              <div class="Polaris-InlineStack" style="--pc-inline-stack-align:start;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:nowrap;--pc-inline-stack-gap-xs:var(--p-space-0);--pc-inline-stack-gap-md:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row">
                <form name="frmPagination" id="frmPagination">
                <div class="Polaris-IndexFilters__ActionWrap">
                  <input type="search" id="q" name="q" class="form-controls" value="{{q}}" placeholder="Search by product name">
                  <button onclick="upload_image()" class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconWithText" type="button">Search</button>
                    <span class="">
                      <a href="https://admin.shopify.com/store/{{shop_name}}/products/new" target="_blank" class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter" type="button">
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Add product</span>
                      </a>
                      
                    </span>
                    <span>
                      <button onclick="upload_image()" class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconWithText" type="button">
                        <span class="Polaris-Button__Icon">
                          <span class="Polaris-Icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M16.5 6.26a.75.75 0 0 1-1.5 0v-.51a.75.75 0 0 0-.75-.75h-8.5a.75.75 0 0 0-.75.75v.51a.75.75 0 0 1-1.5 0v-.51a2.25 2.25 0 0 1 2.25-2.25h8.5a2.25 2.25 0 0 1 2.25 2.25v.51Z"/><path d="M10.75 16.01a.75.75 0 0 1-1.5 0v-6.69l-1.72 1.72a.75.75 0 1 1-1.06-1.06l3-3a.75.75 0 0 1 1.06 0l3 3a.75.75 0 1 1-1.06 1.06l-1.72-1.72v6.69Z"/></svg>
                          </span>
                        </span>
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Filepicker</span>
                      </button>
                    </span>

                    <span class="">
                        <select name="page_limit" id="page_limit" onchange="submit()">
                        <option value="20" {% if page_limit == '20' %} selected {% endif %}>20</option>
                        <option value="50" {% if page_limit == '50' %} selected {% endif %}>50</option>
                        <option value="100" {% if page_limit == '100' %} selected {% endif %}>100</option>
                        <option value="200" {% if page_limit == '200' %} selected {% endif %}>200</option>
                        <option value="500" {% if page_limit == '500' %} selected {% endif %}>500</option>
                      </select> &nbsp;
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Records per page</span>
                    </span>
                </div>
              </form>
              </div>
            </div>
          </div>
          <div>
          </div>
        </div>
      </div>
      <div class="Polaris-IndexTable">
        <div class="Polaris-IndexTable__IndexTableWrapper">
          <div class="Polaris-IndexTable__LoadingPanel">
            <div class="Polaris-IndexTable__LoadingPanelRow">
              <span class="Polaris-Spinner Polaris-Spinner--sizeSmall">
                <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.229 1.173a9.25 9.25 0 1011.655 11.412 1.25 1.25 0 10-2.4-.698 6.75 6.75 0 11-8.506-8.329 1.25 1.25 0 10-.75-2.385z">
                  </path>
                </svg>
              </span>
              <span role="status">
                <span class="Polaris-Text--root Polaris-Text--visuallyHidden">
                </span>
              </span>
              <span class="Polaris-IndexTable__LoadingPanelText">Loading orders…</span>
            </div>
          </div>
          <div class="Polaris-IndexTable__StickyTable" role="presentation">
            <div>
              <div>
              </div>
              <div>
                <div class="Polaris-IndexTable__StickyTableHeader">
                  <div class="Polaris-IndexTable__LoadingPanel">
                    <div class="Polaris-IndexTable__LoadingPanelRow">
                      <span class="Polaris-Spinner Polaris-Spinner--sizeSmall">
                        <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M7.229 1.173a9.25 9.25 0 1011.655 11.412 1.25 1.25 0 10-2.4-.698 6.75 6.75 0 11-8.506-8.329 1.25 1.25 0 10-.75-2.385z">
                          </path>
                        </svg>
                      </span>
                      <span role="status">
                        <span class="Polaris-Text--root Polaris-Text--visuallyHidden">
                        </span>
                      </span>
                      <span class="Polaris-IndexTable__LoadingPanelText">Loading orders…</span>
                    </div>
                  </div>
                  <div class="Polaris-IndexTable__StickyTableHeadings">
                    <div class="Polaris-IndexTable__TableHeading Polaris-IndexTable__TableHeading--first" data-index-table-sticky-heading="true" style="min-width: 38px;">
                      <div class="Polaris-IndexTable__ColumnHeaderCheckboxWrapper">
                        <label class="Polaris-Choice Polaris-Choice--labelHidden Polaris-Checkbox__ChoiceLabel" for=":R2cqakq6:">
                          <span class="Polaris-Choice__Control">
                            <span class="Polaris-Checkbox">
                              <input id=":R2cqakq6:" type="checkbox" class="Polaris-Checkbox__Input" aria-invalid="false" role="checkbox" aria-checked="false" value="">
                              <span class="Polaris-Checkbox__Backdrop">
                              </span>
                              <span class="Polaris-Checkbox__Icon Polaris-Checkbox--animated">
                                <svg viewBox="0 0 16 16" shape-rendering="geometricPrecision" text-rendering="geometricPrecision">
                                  <path class="" d="M1.5,5.5L3.44655,8.22517C3.72862,8.62007,4.30578,8.64717,4.62362,8.28044L10.5,1.5" transform="translate(2 2.980376)" opacity="0" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" pathLength="1">
                                  </path>
                                </svg>
                              </span>
                            </span>
                          </span>
                          <span class="Polaris-Choice__Label">
                            <span class="Polaris-Text--root Polaris-Text--bodyMd">Select all orders</span>
                          </span>
                        </label>
                      </div>
                    </div>
                    <div class="Polaris-IndexTable__TableHeading Polaris-IndexTable__TableHeading--second" data-index-table-sticky-heading="true" style="left: 38px; min-width: 70px;">
                      <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Order</span>
                      </div>
                    </div>
                    <div class="Polaris-IndexTable__TableHeading" data-index-table-sticky-heading="true" style="min-width: 160px;">
                      <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Date</span>
                      </div>
                    </div>
                    <div class="Polaris-IndexTable__TableHeading" data-index-table-sticky-heading="true" style="min-width: 161px;">
                      <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Customer</span>
                      </div>
                    </div>
                    <div class="Polaris-IndexTable__TableHeading Polaris-IndexTable--tableHeadingAlignEnd" data-index-table-sticky-heading="true" style="min-width: 90px;">
                      <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Total</span>
                      </div>
                    </div>
                    <div class="Polaris-IndexTable__TableHeading" data-index-table-sticky-heading="true" style="min-width: 155px;">
                      <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Payment status</span>
                      </div>
                    </div>
                    <div class="Polaris-IndexTable__TableHeading Polaris-IndexTable__TableHeading--last" data-index-table-sticky-heading="true" style="min-width: 159px;">
                      <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                        <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Fulfillment status</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="Polaris-IndexTable__BulkActionsWrapper">
                  <div>
                    <div class="Polaris-InlineStack" style="--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-400);--pc-inline-stack-flex-direction-xs:row">
                      <div class="Polaris-BulkActions__BulkActionsSelectAllWrapper">
                        <div class="Polaris-CheckableButton">
                          <div class="Polaris-CheckableButton__Checkbox">
                            <label class="Polaris-Choice Polaris-Choice--labelHidden Polaris-Checkbox__ChoiceLabel" for=":R1baakq6:">
                              <span class="Polaris-Choice__Control">
                                <span class="Polaris-Checkbox">
                                  <input id=":R1baakq6:" type="checkbox" class="Polaris-Checkbox__Input" aria-invalid="false" role="checkbox" aria-checked="false" value="">
                                  <span class="Polaris-Checkbox__Backdrop">
                                  </span>
                                  <span class="Polaris-Checkbox__Icon Polaris-Checkbox--animated">
                                    <svg viewBox="0 0 16 16" shape-rendering="geometricPrecision" text-rendering="geometricPrecision">
                                      <path class="" d="M1.5,5.5L3.44655,8.22517C3.72862,8.62007,4.30578,8.64717,4.62362,8.28044L10.5,1.5" transform="translate(2 2.980376)" opacity="0" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" pathLength="1">
                                      </path>
                                    </svg>
                                  </span>
                                </span>
                              </span>
                              <span class="Polaris-Choice__Label">
                                <span class="Polaris-Text--root Polaris-Text--bodyMd">Select all 3 orders</span>
                              </span>
                            </label>
                          </div>
                          <span class="Polaris-CheckableButton__Label">
                            <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">0 selected</span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="Polaris-IndexTable-ScrollContainer">
            <table class="Polaris-IndexTable__Table Polaris-IndexTable__Table--sticky">
              <thead>
                <tr>
                  <th class="Polaris-IndexTable__TableHeading Polaris-IndexTable__TableHeading--first" data-index-table-heading="true">
                  </th>
                  <th class="Polaris-IndexTable__TableHeading Polaris-IndexTable__TableHeading--second" data-index-table-heading="true" style="left: 38px;">
                    <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                      <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Product</span>
                    </div>
                  </th>
                  <th class="Polaris-IndexTable__TableHeading" data-index-table-heading="true">
                    <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                      <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Price</span>
                    </div>
                  </th>
                  <th class="Polaris-IndexTable__TableHeading" data-index-table-heading="true">
                    <div style="--pc-index-table-heading-extra-padding-right:0" class="">
                      <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">SKU Number</span>
                    </div>
                  </th>
                  <th class="Polaris-IndexTable__TableHeading Polaris-IndexTable__TableHeading--last" data-index-table-heading="true">
                    <div style="display: flex; justify-content: center;" class="">
                      <span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Upload/View</span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody id="product-list">
               
              </tbody>
              <tr><td colspan="5">
                <div>
                  <span class="previous-button">
                    <button onclick="set_page_mode('prev')" id="a_previous_link" class="paginate-button">Previous</button>
                  </span>
                  <span class="next-button">
                    <button onclick="set_page_mode('next')" id="a_next_link" class="paginate-button">Next</button>
                  </span>
                </div>
              </td></tr>
            <input type="hidden" name="page_mode" id="page_mode" />
            </table>
          </div>
          <div class="Polaris-IndexTable__ScrollBarContainer Polaris-IndexTable--scrollBarContainerHidden">
            <div class="Polaris-IndexTable__ScrollBar" style="--pc-index-table-scroll-bar-content-width: 816px;">
              <div class="Polaris-IndexTable__ScrollBarContent">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal -->
    <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
      aria-hidden="true" id="filepickers">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <div>Filestack Upload</div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">×</span>
            </button>
          </div>
          <div class="modal-body">
            <div>
              <! jDOCTYPE html>
                <html lang="en">

                <head>
                  <meta charset="UTF-8">
                  <meta http-equiv="X-UA-Compatible" content="IE=edge">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <div id="inline" style="height:500px;"></div>
                  <pre id="res" style="border: 1px solid red; padding: 5p; background-color: #ccc;"></pre>
                  <script src="https://static.filestackapi.com/filestack-js/3.x.x/filestack.min.js"></script>
                  <title>Document</title>
                </head>

                <body>
                  <script>
                    window.addEventListener('DOMContentLoaded', onDOMContentLoaded); //DOMContentLoaded

                    function onDOMContentLoaded() {
                      const apikey = '{{filestack_obj.apikey}}'; //devportal api_key
                      let initOptions = {};

                      if ('{{ ENVIRONMENT }}' == 'STAGE') {
                        initOptions.cname = 'stage.filestackapi.com';
                      }

                      const client = filestack.init(apikey, initOptions);
                      const pickerOpt = {};

                      pickerOpt.container = '#inline';
                      pickerOpt.displayMode = 'inline';
                      pickerOpt.onUploadDone = (res) => {
                        $('#filepickers').hide();
                        for (let i = 0; i < res.filesUploaded.length; i++) {
                          if (res && res.filesUploaded[i].url) {
                            $('#image_url').val(res.filesUploaded[i].url);
                            upload_image_shopify();
                          }
                        }
                        alert('Image has been uploaded');
                        window.location.href = '/images';
                      }

                      const fromSources = {{ filestack_obj.from_sources|ensure_array|safe }};
                      if (Array.isArray(fromSources) && fromSources.length > 0) {
                        pickerOpt.fromSources = fromSources;
                      }

                      const accept = {{ filestack_obj.accept_file_types|ensure_array|safe }};
                      if (Array.isArray(accept) && accept.length > 0) {
                        pickerOpt.accept = accept;
                      }

                      const maxSize = {{ filestack_obj.max_filesize|ensure_number|safe}};
                      if (maxSize !== '') {
                        pickerOpt.maxSize = maxSize;
                      }

                      const maxFiles = {{ filestack_obj.max_files|ensure_number|safe}};
                      if (maxFiles !== '') {
                        pickerOpt.maxFiles = maxFiles;
                      }

                      const imageMax = {{ filestack_obj.image_dimension|ensure_array|safe}};
                      if (Array.isArray(imageMax) && imageMax.length == 2) {
                        pickerOpt.imageMax = imageMax;
                      }

                      const imageDim = {{ filestack_obj.max_image_dimension|ensure_array|safe}};
                      if (Array.isArray(imageDim) && imageDim.length == 2) {
                        pickerOpt.imageDim = imageDim;
                      }

                      //TODO: Image editor image_editor

                      const transformation_crop = {% if filestack_obj.transformation_crop %}true{% else %}false{% endif %};
                      const transformation_resize = {% if filestack_obj.transformation_resize %}true{% else %}false{% endif %};
                      const transformation_rotate = {% if filestack_obj.transformation_rotate %}true{% else %}false{% endif %};
                      if (transformation_crop || transformation_resize || transformation_rotate) {
                        pickerOpt.transformations = {};
                        pickerOpt.transformations.crop = transformation_crop
                        pickerOpt.transformations.resize = transformation_resize
                        pickerOpt.transformations.rotate = transformation_rotate
                      }

                      const uploadRetry = {{ filestack_obj.num_retry|ensure_number|safe}};
                      if (Number.isInteger(uploadRetry) && uploadRetry >= 0 && uploadRetry <= 20) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.retry = uploadRetry;
                      }

                      const uploadConcurrency = {{ filestack_obj.num_concurrency|ensure_number|safe}};
                      // For following validation refer - https://github.com/filestack/filestack-js/blob/c9a366248ce701748a5411e34ee3c0f8d54d4e43/src/schema/upload.schema.ts#L26
                      if (Number.isInteger(uploadConcurrency) && uploadConcurrency > 0 && uploadConcurrency <= 20) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.concurrency = uploadConcurrency;
                      }

                      const uploadTimeout = {{ filestack_obj.error_timeout|ensure_number|safe}};
                      if (Number.isInteger(uploadTimeout) && uploadTimeout > 0) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.timeout = uploadTimeout;
                      }

                      const uploadIntegrity = {% if filestack_obj.intigintergrity_check %}true{% else %}false{% endif %};
                      if (uploadIntegrity) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.disableIntegrityCheck = uploadIntegrity;
                      }

                      const uploadIntelligent = {% if filestack_obj.intelligent %}true{% else %}false{% endif %};
                      if (uploadIntelligent) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.intelligent = uploadIntelligent;
                      }

                      const uploadIntelligentChunkSize = {{ filestack_obj.chunk_size|ensure_number|safe}};
                      if (Number.isInteger(uploadIntelligentChunkSize) && uploadIntelligentChunkSize > 0) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.intelligentChunkSize = uploadIntelligentChunkSize;
                      }

                      // partsize
                      const uploadPartSize = {{ filestack_obj.part_size|ensure_number|safe}};
                      if (Number.isInteger(uploadPartSize) && uploadPartSize > 5 * 1024 * 1024) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.partSize = uploadPartSize;
                      }

                      // progress interval
                      const uploadProgressInterval = {{ filestack_obj.progress_interval|ensure_number|safe}};
                      if (Number.isInteger(uploadProgressInterval) && uploadProgressInterval > 0) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.progressInterval = uploadProgressInterval;
                      }

                      // retry factor
                      const uploadRetryFactor = {{ filestack_obj.retry_factor|ensure_number|safe}};
                      if (Number.isInteger(uploadRetryFactor) && uploadRetryFactor > 0) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.retryFactor = uploadRetryFactor;
                      }

                      // retry max time
                      const uploadRetryMaxTime = {{ filestack_obj.retry_maxtime|ensure_number|safe}};
                      if (Number.isInteger(uploadRetryMaxTime) && uploadRetryFactor >= 0) {
                        if (!pickerOpt.uploadConfig) {
                          pickerOpt.uploadConfig = {}
                        }
                        pickerOpt.uploadConfig.retryMaxTime = uploadRetryMaxTime;
                      }

                      const allowManualRetry = {% if filestack_obj.allow_manual_retry %}true{% else %}false{% endif %};
                      if (allowManualRetry) {
                        pickerOpt.allowManualRetry = allowManualRetry;
                      }

                      const disableTransformer = {% if filestack_obj.disable_transformer %}true{% else %}false{% endif %};
                      if (disableTransformer) {
                        pickerOpt.disableTransformer = disableTransformer;
                      }

                      // language
                      const lang = "{{ filestack_obj.language|ensure_string|safe }}";
                      if (typeof lang === 'string' && lang.length == 2) {
                        pickerOpt.lang = lang;
                      }

                      const minFiles = {{ filestack_obj.min_files|ensure_number|safe}};
                      // Refer - https://github.com/filestack/filestack-js/blob/c9a366248ce701748a5411e34ee3c0f8d54d4e43/src/schema/picker.schema.ts
                      if (Number.isInteger(minFiles) && minFiles > 0 && minFiles <= 1000000) {
                        pickerOpt.minFiles = minFiles;
                      }

                      // supportEmail
                      const supportEmail = "{{ filestack_obj.support_email|ensure_string|safe }}";
                      if (typeof supportEmail === 'string' && supportEmail.length > 0) {
                        pickerOpt.supportEmail = supportEmail;
                      }

                      const initialState = {};
                      console.log(pickerOpt)
                      const picker = client.picker(pickerOpt);
                      picker.open();
                    }

                    function upload_image_shopify(){

                      var productId = $('#product_id').val();
                      var url = productId ? '/upload-product-images' : '/upload-image-file';

                      $.ajax({
                        type: "POST",
                        url: url,
                        dataType: 'json',
                        data: $('#frmProductId').serialize(), // serializes the form's elements.
                        success: function (res) {
                          // alert('Image has been uploaded');
                        }
                      });
                    }

                    function upload_image(product_id) {
                      $('#product_id').val(product_id);
                      $('#filepickers').modal('show');
                      $('.bd-example-modal-lg').addClass('show')
                    }
                  </script>
                </body>

                </html>
            </div>
          </div>

        </div>
      </div>
    </div>


  </div>
</div>

<form name="frmProductId" id="frmProductId">
  <input type="hidden" name="image_url" id="image_url" />
  <input type="hidden" name="product_id" id="product_id" />
</form>
<script>
  function set_page_mode(page_mode)
  {
    $('#page_mode').val(page_mode);
    call_ajax(page_mode);
  }
$(document).ready(function (){
  var page_mode = $('#page_mode').val();
  call_ajax(page_mode);
  return false;
});
function call_ajax(page_mode){
  $('#a_next_link').attr('disabled',false);
  $('#a_previous_link').attr('disabled',false);
  var page_limit = $('#page_limit').val();
  var q =  $('#q').val();
  var next_link = $('#next_link').val();
  var previous_link = $('#previous_link').val();
  if(next_link == '' && page_mode == 'next'){
    $('#a_next_link').attr('disabled',true);
    return false;
  }
  if(previous_link == '' && page_mode == 'prev'){
    $('#a_previous_link').attr('disabled',true);
    return false;
  }
  $.ajax({                                      
      url: '/fetch-product-list?page_mode='+page_mode,              
      type: "get",          
      dataType: 'html',
      data: {'next_link':next_link,'previous_link':previous_link,'page_limit':page_limit,'q':q},                
      beforeSend: function() {
          $('#product-list').html('<tr><td colspan="2" style="text-align:cente;font-weight:bold;font-size:20">Products is loading....</td></tr>');
          },
      success: function(response){
        $('#product-list').html(response)
      } 
   });
}
</script>
{% endblock %}