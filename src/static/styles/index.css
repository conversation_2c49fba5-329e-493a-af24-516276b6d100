*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Inter", sans-serif;
}
body{
    background: #efefef;
}
.header{
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    color: #363636;
}
.header h3{
    font-weight: 500;
    font-size: 20px;
    color: #363636;
}
.header button{
    font-size: 14px;
    padding: 6px 10px;
    background: #fff;
    border: 1px solid #CCC;
    border-radius: 6px;
    cursor: pointer;
}
.main-container{
    width: 80%;
    margin: 0 auto;
    padding: 40px 0px;
    display: flex;
    flex-direction: column;
    gap: 40px;
}
.card{
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.15);

}
.signup-container-text{
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.signup-container-text h3{
    font-weight: 500;
    font-size: 18px;
}
.signup-container-text p{
    font-size: 14px;
}
.signup-buttons{
    display: flex;
    gap: 10px;

}
.black-btn{
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    border: none;
    font-weight: 500;
    background-color: #000;
    color: #fff;
    min-width: 82px;
    height: 28px;

}
.grey-btn{
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    background-color: #fff;
    border: 1px solid #CCC;
    height: 28px;
    width: 83px;
}
.setting-container{
    display: flex;
    gap: 20px;
}
.setting-tabs{
    width: 25%;
}
.settingContent-container{
    width: 75%;
    display: flex;
    flex-direction: column;
    gap: 35px;
}
.account-setting, .storage-setting{
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.label-input{
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.label-input label{
    font-size: 14px;
}
.label-input input {
    height: 36px;
    border-radius: 6px;
    border: 1px solid #7E8489;
    padding:6px 10px;
    font-size: 13px;
}
.anchor-links{
    text-decoration: none;
    font-size: 14px;
    display: inline-block;
    color: #1f5199;
}

.helper-text{
    font-size: 14px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    color: #555;
    line-height: normal;
}
.tabs{
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.15);
}
.tabBtn{
    text-align: start;
    padding: 14px 18px;
    border: none;
    border-bottom: 1px solid #ddd;
    font-size: 16px;
    background-color: #fff;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    color: black;

}
.activeTab{
    color: #2463bc;
    position: relative;

}
.activeTab::before{
    content: "";
    position: absolute;
    width: 3px;
    background: #2463bc;
    height: 100%;
    top: 0;
    left: 0;

}
.uploader-setting{
    display: flex;
    flex-direction: column;
    gap: 40px;
}
.uploaderSetting-1,
.uploaderSetting-2,
.uploaderSetting-3,
.uploaderSetting-4{
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.checkbox-heading,
.selectBox-heading{
    font-size: 14px;
}
.checkbox-container{
    display: flex;
    flex-wrap: wrap;
    column-gap: 20px;
    row-gap: 10px;
    justify-content: flex-start;
}
.checkbox-label{
    min-width: 170px;
    display: flex;
    align-items: center;
    gap: 7px;
    text-transform: capitalize;
    font-size: 14px;
}
input[type="checkbox"]{
    height: 14px;
    width: 14px;
    border: solid 1.5px #ccc;
    appearance: none;
    -webkit-appearance: none;
    border-radius: 3px;
    display: flex;
    justify-content: center;
    align-items: center;


}
input[type="checkbox"]::after{
    content: "\f00c";
    font-family: "Font Awesome 6 Free";
    font-weight: bolder;
    font-size: 8px;
    color: #fff;
}
input[type="checkbox"]:checked{
    background-color: #2463bc;
    border: solid 1.5px #2463bc;
    color: #fff;

}
.radioBox-container{
    display: flex;
    gap: 20px;
}
.radioBox-label{
    display: flex;
    gap: 7px;
    text-transform: capitalize;
    font-size: 14px;
    align-items: center;
}
input[type="radio"]{
    appearance: none;
    -webkit-appearance: none;
    border: solid 2px #ccc;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

input[type="radio"]:checked{
    border: solid 5px #2463bc;
}
.editing-options{
    min-width: auto;
}
.upload-heading{
    font-size: 18px;
    font-weight: 500;
}
.keyValue-container{
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.KeyValue-label{
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 8px;
}
.KeyValue-label label{
    font-size: 14px;
}
.keyValueBtn{
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.key-input{
    height: 36px;
    border-radius: 6px;
    border: 1px solid #7E8489;
    padding:6px 10px;
    font-weight: 500;
    font-size: 13px;
}
.delete-btn{
    background-color: #fff;
    border: 1px solid #ccc;
    letter-spacing: 0.26px;
    padding: 6px 10px;
    height: 36px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
}
.addTag-btn{
    height: 28px;
    width: max-content;
    padding: 6px 10px;
    border-radius: 6px;
    border: 1px solid #ccc;
    background: #fff;
    cursor: pointer;
}
.pkr-checkbox-cont{
    display: flex;
    flex-direction: column;
    gap: 20px;
}
.pkr-checkbox{
    display: flex;
    gap:7px;
    align-items: flex-start;
}
.pkr-label{
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 6px;


}
.pkr-label label{
    font-size: 14px;
    line-height: 14px;
}
.pkr-label p{
    font-size: 14px;
    color: #555;
}
.label-dropdown{
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.label-dropdown label{
    font-size: 14px;
}
.custom-dropdown{
    position: relative;
    height: 36px;
    border-radius: 6px;
    border: solid 1px #7e8489;
    display: flex;
    align-items: center;
}
.dropdown{
    -webkit-appearance: none;
    appearance: none;
    height: 100%;
    width: 100%;
    padding: 6px 10px;
    border-radius: 6px;
    border: none;
    text-transform: capitalize;

}
.custom-icon{
    position: absolute;
    right: 10px;
    width: 14px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
}

/* Media Query for screens between 360px to 414px */
 @media only screen and (min-width: 360px) and (max-width: 428px) {
    .main-container{
        width: 100%;
        padding: 30px 20px;
    }
    .setting-container{
        flex-direction: column;
    }
    .setting-tabs{
        width: 100%;
    }
    .tabs{
        flex-direction: row;
    }
    .tabBtn{
        text-align: center;
        padding: 14px 0px;
        border-right: 1px solid #ddd;
        border-bottom: none;

    }
    .activeTab::before{
        width: 100%;
        height: 3px;
        top: 95%;

    }
    .settingContent-container{
        width: 100%;
    }
    .account-setting, .storage-setting{
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .keyValue-container{
        display: flex;
        flex-direction: column;
        gap: 25px;

    }
    .keyValueBtn{
        flex-direction: column;
        align-items:flex-start;
    }
    .KeyValue-label{
        width: 100%;
    }

 }
 
 .dropdown-list {
    height: 35px;
  }