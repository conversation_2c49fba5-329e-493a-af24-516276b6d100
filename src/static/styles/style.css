body {
  background: #f1f1f1 !important;
}

/* Style the tab */
.tab {
  overflow: hidden;
  border: 1px solid #ccc;
  background-color: #f1f1f1;
}

/* Style the buttons that are used to open the tab content */
.tab button {
  background-color: inherit;
  float: left;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  transition: 0.3s;
  padding-left: 30px;
  padding-right: 30px;
}

/* Change background color of buttons on hover */
.tab button:hover {
  background-color: #ddd;
}

/* Create an active/current tablink class */
.tab button.active {
  padding-left: 30px;
  padding-right: 30px;
}

/* Style the tab content */
.tabcontent {
  display: block;
  padding: 6px 12px;
  border-top: none;
}

.main-container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}



.tab input {
  position: absolute;
  opacity: 0;
  z-index: -1;
}

.tab__content {
  max-height: 0;
  overflow: hidden;
  transition: all 0.35s;
}

.tab input:checked~.tab__content {
  max-height: none;
}

.accordion {
  overflow: hidden;
  padding: 0 15%;
}

section.accordion .tab {
  background: none;
  border: none;
}

.tab__label,
.tab__close {
  display: flex;
  color: #000000;
  cursor: pointer;
}

.tab__label {
  justify-content: space-between;
  padding: 10px 0px;
  border-top: 1px solid #cccccc;
  font-size: 16px;
}

.tab__label p {
  max-width: 85%;
}

.tab__label::after {
  content: "\276F";
  width: 1em;
  height: 1.2em;
  text-align: center;
  transform: rotate(90deg);
  transition: all 0.35s;
}

.tab input:checked+.tab__label::after {
  transform: rotate(270deg);
}

.tab__content p {
  margin: 0;
  padding: 20px 0px;
  line-height: 22px;
  font-size: 14px;
}

.tab__content li {
  line-height: 22px;
  font-size: 14px;
}

.tab__close {
  justify-content: flex-end;
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.accordion--radio {
  --theme: var(--secondary);
}



.reports-container {
  display: flex;
  gap: 10px;
}

.reports-content {
  background-color: #fff;
  width: 33%;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.15);
}

.reports-content p {
  font-size: 12px;
  color: #555555;
}

.reports-content span {
  font-size: 12px;
}

th {
  text-align: center;
}

.tab {
  padding-left: 10px;
  padding-right: 15px;
  display: block;
}